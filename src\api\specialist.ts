import apiClient from '@/src/utils/apiClient';
import { SPECIALIST_ENDPOINTS, ROLES_ENDPOINTS } from '@/src/utils/apiRoutes';
import { extractApiErrorMessage, extractApiResponseData, extractApiResponseArray } from '@/src/utils/apiErrorHandler';

export interface Specialist {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    specialist_addresses?: string;
    created_at: string;
    updated_at: string;
}

export interface CreateSpecialistInput {
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    specialist_addresses?: string;
    role_id: number;
}

export interface Role {
    id: number;
    role_name: string;
    created_at: string;
    updated_at: string;
}

export const getRoles = async (): Promise<Role[]> => {
    try {
        const response = await apiClient.get(ROLES_ENDPOINTS.GET_ROLES);
        return response.data.data as Role[];
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not fetch roles'));
    }
};
// List specialists
export const getAllSpecialists = async (): Promise<Specialist[]> => {
    try {
        const response = await apiClient.get(SPECIALIST_ENDPOINTS.GET_SPECIALISTS);
        return extractApiResponseArray<Specialist>(response, 'Unexpected response structure');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not load specialists'));
    }
};

// Create specialist
export const createSpecialist = async (specialistData: CreateSpecialistInput): Promise<Specialist> => {
    try {
        const response = await apiClient.post(SPECIALIST_ENDPOINTS.CREATE_SPECIALIST, specialistData);
        return extractApiResponseData<Specialist>(response, 'No data returned after creation');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not create specialist'));
    }
};

// Update specialist
export const updateSpecialist = async (specialistId: number, specialistData: Partial<CreateSpecialistInput>): Promise<Specialist> => {
    try {
        const response = await apiClient.patch(SPECIALIST_ENDPOINTS.UPDATE_SPECIALIST(specialistId.toString()), specialistData);
        return extractApiResponseData<Specialist>(response, 'No data returned after update');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not update specialist'));
    }
};

// View one specialist
export const viewSpecialist = async (specialistId: number): Promise<Specialist> => {
    try {
        const response = await apiClient.get(SPECIALIST_ENDPOINTS.GET_SPECIALIST(specialistId.toString()));
        return extractApiResponseData<Specialist>(response, 'No specialist data received');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not fetch specialist'));
    }
};

// Delete specialist
export const deleteSpecialist = async (specialistId: number): Promise<void> => {
    try {
        await apiClient.delete(SPECIALIST_ENDPOINTS.DELETE_SPECIALIST(specialistId.toString()));
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not delete specialist'));
    }
};
