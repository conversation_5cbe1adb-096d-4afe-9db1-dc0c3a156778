'use client';

import React, { useState, useEffect, Fragment } from 'react';
import { EyeIcon } from '@heroicons/react/24/outline';
import { Dialog, Transition } from '@headlessui/react';
import { useRouter } from 'next/navigation';
import Loading from '../../../components/layouts/loading';
import Pagination from '../../../components/Pagination';
import toast from 'react-hot-toast';
import { getAllPatients, Patient, updatePatientVersionStatus } from '@/src/api/patient';

const PatientList: React.FC = () => {
    const [search, setSearch] = useState('');
    const [patients, setPatients] = useState<Patient[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [showModal, setShowModal] = useState(false);
    const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
    const [isUpdating, setIsUpdating] = useState(false);
    const router = useRouter();
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    // Removed: const [sortBy, setSortBy] = useState<string>('');
    // Removed: const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

    useEffect(() => {
        const fetchPatients = async () => {
            try {
                setLoading(true);
                setError(null);
                const data = await getAllPatients();
                setPatients(data);
            } catch (err: any) {
                console.error('Error fetching patients:', err);
                setError(err.message || 'An error occurred while fetching patients');
                toast.error(err.message || 'Failed to load patients');
            } finally {
                setLoading(false);
            }
        };

        fetchPatients();
    }, []);

    const handleView = (id: number) => router.push(`/patient/${id}`);

    const confirmStatusToggle = (patient: Patient) => {
        setSelectedPatient(patient);
        setShowModal(true);
    };

    const handleStatusToggle = async () => {
        if (!selectedPatient) return;

        // Find the latest version using is_latest_version field

        const latestVersion = selectedPatient.versions?.find(v => v.is_latest_version);
        if (!latestVersion) {
            toast.error('No latest version found for this patient');
            setShowModal(false);
            setSelectedPatient(null);
            return;
        }

        // Only allow cancelling if the version status is 'sent_by_doctor'
        if (latestVersion.status !== 'sent_by_doctor') {
            toast.error('Only versions with "sent_by_doctor" status can be cancelled');
            setShowModal(false);
            setSelectedPatient(null);
            return;
        }

        try {
            setIsUpdating(true);

            // Cancel the latest version (status will change to 'cancelled_by_admin')
            await updatePatientVersionStatus(selectedPatient.id, latestVersion.id, false);

            // Update the patient in the local state
           setPatients(prev => prev.map(p => {
                if (p.id === selectedPatient.id) {
                    const updatedVersions = p.versions?.map(v =>
                        v.id === latestVersion.id
                            ? { ...v, status: 'cancelled_by_admin' as const }
                            : v
                    ) || [];

                        return {
                            ...p,
                            versions: updatedVersions,
                        };
                    }
                    return p;
                }),
            );

            toast.success('Patient version cancelled successfully');
        } catch (err: any) {
            console.error('Error cancelling patient version:', err);
            toast.error(err.message || 'Failed to cancel patient version');
        } finally {
            setIsUpdating(false);
            setShowModal(false);
            setSelectedPatient(null);
        }
    };

    const filteredPatients = patients.filter((p) => `${p.first_name} ${p.last_name} ${p.email} ${p.dob} ${p.country}`.toLowerCase().includes(search.toLowerCase()));

    // Removed handleSort and sorting logic
    // const handleSort = ...
    // const sorted = ...
    // Pagination logic now uses filteredPatients directly
    const getPaginatedData = () => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return filteredPatients.slice(startIndex, endIndex);
    };

    if (loading) {
        return <Loading />;
    }

    return (
        <div className="p-4 bg-white rounded shadow relative">
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold">Patient List</h2>
            </div>

            {/* Search input after heading */}
            <div className="flex justify-end">
                <input type="text" placeholder="Search" value={search} onChange={(e) => setSearch(e.target.value)} className="form-input w-1/3 text-left" />
            </div>

            {/* Custom Table */}
            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-100">
                        <tr>
                            <th className="py-3 px-4 text-start font-bold text-lg">First Name</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Last Name</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">DOB</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Country</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Status</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {getPaginatedData().map((row: Patient, idx: number) => {
                            // Get the latest version using is_latest_version field
                            const latestVersion = row.versions?.find((v) => v.is_latest_version);
                            const versionStatus = latestVersion?.status;
                            const canCancel = versionStatus === 'sent_by_doctor';

                            return (
                                <tr key={row.id} className={`hover:bg-gray-50 ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'} ${versionStatus === 'cancelled_by_admin' ? 'opacity-60' : ''}`}>
                                    <td className="py-3 px-4 text-sm text-gray-800">{row.first_name}</td>
                                    <td className="py-3 px-4 text-sm text-gray-800">{row.last_name}</td>
                                    <td className="py-3 px-4 text-sm text-gray-800">{row.dob ? row.dob.split('T')[0] : ''}</td>
                                    <td className="py-3 px-4 text-sm text-gray-800">{row.country ? row.country.charAt(0).toUpperCase() + row.country.slice(1).replace('-', ' ') : ''}</td>
                                    <td className="py-3 px-4 text-sm text-gray-800">
                                        <div className="flex items-center gap-2">
                                            <span
                                                className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                    versionStatus === 'sent_by_doctor'
                                                        ? 'bg-yellow-100 text-yellow-800'
                                                        : versionStatus === 'cancelled_by_admin'
                                                          ? 'bg-red-100 text-red-800'
                                                          : versionStatus === 'approved_by_specialist'
                                                            ? 'bg-blue-100 text-blue-800'
                                                            : versionStatus === 'approved_by_doctor'
                                                              ? 'bg-green-100 text-green-800'
                                                              : versionStatus === 'rejected_by_specialist'
                                                                ? 'bg-orange-100 text-orange-800'
                                                                : row.versions?.length === 0
                                                                  ? 'bg-gray-100 text-gray-800'
                                                                  : 'bg-gray-100 text-gray-800'
                                                }`}
                                            >
                                                {versionStatus === 'sent_by_doctor'
                                                    ? 'Pending'
                                                    : versionStatus === 'cancelled_by_admin'
                                                      ? 'Cancelled'
                                                      : versionStatus === 'approved_by_specialist'
                                                        ? 'Approved by Specialist'
                                                        : versionStatus === 'approved_by_doctor'
                                                          ? 'Approved by Doctor'
                                                          : versionStatus === 'rejected_by_specialist'
                                                            ? 'Rejected'
                                                            : row.versions?.length === 0
                                                              ? 'No Version'
                                                              : 'Unknown'}
                                            </span>
                                            {/* {latestVersion && <span className="text-xs text-gray-500">v{latestVersion.version_number}</span>} */}
                                            {canCancel && (
                                                <button
                                                    onClick={() => confirmStatusToggle(row)}
                                                    className="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 bg-red-600 hover:bg-red-700"
                                                    title="Cancel latest version"
                                                >
                                                    <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6" />
                                                </button>
                                            )}
                                        </div>
                                    </td>
                                    <td className="py-3 px-4 text-sm text-gray-800">
                                        <div className="flex gap-3 items-center">
                                            <button onClick={() => handleView(row.id)} title="View">
                                                <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            );
                        })}
                    </tbody>
                </table>
            </div>

            {/* Pagination */}
            <Pagination currentPage={currentPage} totalItems={filteredPatients.length} itemsPerPage={itemsPerPage} onPageChange={setCurrentPage} onItemsPerPageChange={setItemsPerPage} />

            {/* Status Toggle Confirmation Modal */}
            <Transition appear show={showModal} as={Fragment}>
                <Dialog as="div" className="relative z-50" onClose={() => setShowModal(false)}>
                    <Transition.Child as={Fragment} enter="ease-out duration-300" enterFrom="opacity-0" enterTo="opacity-100" leave="ease-in duration-200" leaveFrom="opacity-100" leaveTo="opacity-0">
                        <div className="fixed inset-0 bg-black bg-opacity-25" />
                    </Transition.Child>

                    <div className="fixed inset-0 overflow-y-auto">
                        <div className="flex min-h-full items-center justify-center p-4 text-center">
                            <Transition.Child
                                as={Fragment}
                                enter="ease-out duration-300"
                                enterFrom="opacity-0 scale-95"
                                enterTo="opacity-100 scale-100"
                                leave="ease-in duration-200"
                                leaveFrom="opacity-100 scale-100"
                                leaveTo="opacity-0 scale-95"
                            >
                                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                    {(() => {
                                        const latestVersion = selectedPatient?.versions?.find((v) => v.is_latest_version);
                                        const canCancel = latestVersion?.status === 'sent_by_doctor';

                                        return (
                                            <>
                                                <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                                                    Cancel Patient Version
                                                </Dialog.Title>
                                                <div className="mt-2">
                                                    <p className="text-sm text-gray-500">
                                                        Are you sure you want to cancel version
                                                        {latestVersion && ` ${latestVersion.version_number}`} for{' '}
                                                        <span className="font-semibold">
                                                            {selectedPatient?.first_name} {selectedPatient?.last_name}
                                                        </span>
                                                        ? This action will mark the version as cancelled and cannot be undone.
                                                    </p>
                                                    {!canCancel && <p className="text-sm text-red-600 mt-2">Note: Only versions with "sent_by_doctor" status can be cancelled.</p>}
                                                </div>

                                                <div className="mt-4 flex justify-end gap-3">
                                                    <button className="px-4 py-2 border rounded hover:bg-gray-50 text-black" onClick={() => setShowModal(false)} disabled={isUpdating}>
                                                        Cancel
                                                    </button>
                                                    <button
                                                        className={`px-4 py-2 rounded text-white bg-red-600 hover:bg-red-700 ${isUpdating || !canCancel ? 'opacity-50 cursor-not-allowed' : ''}`}
                                                        onClick={handleStatusToggle}
                                                        disabled={isUpdating || !canCancel}
                                                    >
                                                        {isUpdating ? 'Cancelling...' : 'Cancel Version'}
                                                    </button>
                                                </div>
                                            </>
                                        );
                                    })()}
                                </Dialog.Panel>
                            </Transition.Child>
                        </div>
                    </div>
                </Dialog>
            </Transition>
        </div>
    );
};

export default PatientList;
