'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { useRouter, useSearchParams } from 'next/navigation';
import { createPlan, updatePlan, viewPlan, Plan, CreatePlanInput, UpdatePlanInput } from '@/src/api/plans';
import toast from 'react-hot-toast';

interface PlansFormProps {
    planId?: number;
    mode?: 'view' | 'edit';
}

// helper to capitalize first letter for display
const capitalize = (s: string) => (s.length > 0 ? s.charAt(0).toUpperCase() + s.slice(1) : '');

const PlansForm: React.FC<PlansFormProps> = ({ planId, mode: propMode }) => {
    const {
        control,
        handleSubmit,
        setValue,
        watch,
        formState: { errors },
    } = useForm<Plan>({
        defaultValues: { name: '', type: '', duration_years: undefined },
    });

    const router = useRouter();
    const searchParams = useSearchParams();
    const mode = propMode || (searchParams.get('mode') as 'view' | 'edit' | null);
    const isViewMode = mode === 'view';

    const [loading, setLoading] = useState(false);
    const [plan, setPlan] = useState<Plan | null>(null);

    // watch type to conditionally show duration
    const selectedType = watch('type');
    const [typeDropdownOpen, setTypeDropdownOpen] = useState(false);
    const typeDropdownRef = useRef<HTMLDivElement>(null);

    // Close dropdown on outside click
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (typeDropdownRef.current && !typeDropdownRef.current.contains(event.target as Node)) {
                setTypeDropdownOpen(false);
            }
        }
        if (typeDropdownOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [typeDropdownOpen]);

    // fetch existing plan in view/edit
    useEffect(() => {
        if (planId) {
            const fetchPlan = async () => {
                try {
                    setLoading(true);
                    const data = await viewPlan(planId);
                    setPlan(data);
                    setValue('name', data.name);
                    setValue('type', data.type);
                    setValue('duration_years', data.duration_years);
                } catch {
                    toast.error('Failed to fetch plan details');
                } finally {
                    setLoading(false);
                }
            };
            fetchPlan();
        }
    }, [planId, setValue]);

    const onSubmit = async (data: Plan) => {
        try {
            setLoading(true);
            if (planId) {
                const payload: UpdatePlanInput = {
                    name: data.name as string,
                    type: data.type as string,
                    ...(data.type !== 'retainer' && { duration_years: data.duration_years }),
                };
                await updatePlan(planId, payload);
                toast.success('Plan updated successfully');
            } else {
                const payload: CreatePlanInput = {
                    name: data.name as string,
                    type: data.type as string,
                    ...(data.type !== 'retainer' && { duration_years: data.duration_years }),
                };
                await createPlan(payload);
                toast.success('Plan created successfully');
            }
            router.push('/plans');
        } catch (error: any) {
            toast.error(error.message || 'An error occurred');
        } finally {
            setLoading(false);
        }
    };

    // view-only mode
    if (isViewMode && plan) {
        return (
            <div className="mx-auto card">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-bold">Plan Details</h2>
                    <div className="flex-gap">
                        <button type="button" onClick={() => router.push(`/plans/${planId}?mode=edit`)} className="btn-primary">
                            Edit Plan
                        </button>
                        <button type="button" onClick={() => router.back()} className="btn-secondary">
                            Back
                        </button>
                    </div>
                </div>
                <div className="grid-2">
                    <div>
                        <label className="form-label">Name</label>
                        <div className="form-input bg-disabled">{plan.name}</div>
                    </div>
                    <div>
                        <label className="form-label">Type</label>
                        <div className="form-input bg-disabled">{capitalize(plan.type)}</div>
                    </div>
                    {plan.type !== 'retainer' && (
                        <div>
                            <label className="form-label">Duration (years)</label>
                            <div className="form-input bg-disabled">{plan.duration_years}</div>
                        </div>
                    )}
                </div>
            </div>
        );
    }

    // add/edit form
    return (
        <div className="mx-auto card">
            <h2 className="text-2xl font-bold mb-6">{planId ? 'Edit Plan' : 'Add Plan'}</h2>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid-2">
                    <div>
                        <label htmlFor="name" className="form-label">
                            Name
                        </label>
                        <Controller
                            name="name"
                            control={control}
                            rules={{
                                required: 'Name is required',
                                maxLength: { value: 100, message: 'Name cannot exceed 100 characters' },
                            }}
                            render={({ field }) => <input {...field} id="name" placeholder="Enter plan name" className="form-input" disabled={isViewMode} maxLength={100} />}
                        />
                        {errors.name && <p className="form-error">{errors.name.message}</p>}
                    </div>

                    <div>
                        <label htmlFor="type" className="form-label">
                            Type
                        </label>
                        <Controller
                            name="type"
                            control={control}
                            rules={{ required: 'Type is required' }}
                            render={({ field }) => (
                                <div ref={typeDropdownRef} className="relative">
                                    <button
                                        type="button"
                                        className={`w-full border rounded-md px-3 py-2 text-left focus:outline-none focus:ring-2 focus:ring-[#eb6309] ${field.value ? 'text-black' : 'text-gray-400'} bg-white`}
                                        onClick={() => !isViewMode && mode !== 'edit' && setTypeDropdownOpen((open) => !open)}
                                        disabled={isViewMode || mode === 'edit'}
                                    >
                                        {field.value ? capitalize(field.value) : 'Select type'}
                                        <span className="float-right">▼</span>
                                    </button>
                                    {typeDropdownOpen && !isViewMode && mode !== 'edit' && (
                                        <div className="absolute z-10 mt-1 w-full bg-white border rounded-md shadow-lg">
                                            <div
                                                className="px-4 py-2 cursor-pointer hover:bg-[#eb6309] hover:text-white rounded-t "
                                                onClick={() => {
                                                    field.onChange('aligner');
                                                    setTypeDropdownOpen(false);
                                                }}
                                            >
                                                Aligner
                                            </div>
                                            <div
                                                className="px-4 py-2 cursor-pointer hover:bg-[#eb6309] hover:text-white rounded-b "
                                                onClick={() => {
                                                    field.onChange('retainer');
                                                    setTypeDropdownOpen(false);
                                                }}
                                            >
                                                Retainer
                                            </div>
                                        </div>
                                    )}
                                </div>
                            )}
                        />
                        {errors.type && <p className="form-error">{errors.type.message}</p>}
                    </div>

                    {selectedType !== 'retainer' && (
                        <div>
                            <label htmlFor="duration_years" className="form-label">
                                Duration (years)
                            </label>
                            <Controller
                                name="duration_years"
                                control={control}
                                rules={{
                                    required: 'Duration is required',
                                    pattern: { value: /^[0-9]*$/, message: 'Please enter a valid number' },
                                    min: { value: 1, message: 'Duration must be at least 1 years' },
                                    max: { value: 10, message: 'Duration cannot exceed 10 years' },
                                }}
                                render={({ field }) => (
                                    <input {...field} value={field.value ?? ''} id="duration_years" type="number" placeholder="Enter duration in years" className="form-input" disabled={isViewMode} />
                                )}
                            />
                            {errors.duration_years && <p className="form-error">{errors.duration_years.message}</p>}
                        </div>
                    )}
                </div>

                <div className="flex-gap pt-4">
                    <button type="submit" disabled={loading} className="btn-primary">
                        {loading ? 'Saving...' : planId ? 'Update Plan' : 'Create Plan'}
                    </button>
                    <button type="button" onClick={() => router.back()} disabled={loading} className="btn-secondary">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    );
};

export default PlansForm;
