/** @type {import('next').NextConfig} */
const nextConfig = {
    reactStrictMode: true,
    swcMinify: true,
    eslint: {
        ignoreDuringBuilds: true,
    },
    images: {
        remotePatterns: [
            {
                protocol: 'http',
                hostname: 'localhost',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'orthodonticbackend.desinoir.com',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'orthodontivc-20250724-oss.oss-me-central-1.aliyuncs.com',
                port: '',
                pathname: '/**',
            },
        ],
    },
};

module.exports = nextConfig;
