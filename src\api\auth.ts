import { AxiosResponse } from 'axios';
import { API_BASE_URL, AUTH_ENDPOINTS } from '@/src/utils/apiRoutes';
import CryptoJs from 'crypto-js';
import { storeCookies, getCookies, clearAllCookies } from '@/src/utils/cookies';
import apiClient from '@/src/utils/apiClient';

// Types for authentication
export interface LoginRequest {
    email: string;
    password: string;
}
export interface LoginResponse {
    success: boolean;
    message: string;
    data?: {
        user: {
            id: string;
            email: string;
            first_name?: string;
            last_name?: string;
            role?: string;
        };
        accessToken: string;
        refreshToken?: string;
    };
    error?: string;
}

export interface User {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
    role?: string;
    profile_image?: string;
}

// Authentication API functions
export const authService = {
    // Login function
    async login(credentials: LoginRequest): Promise<LoginResponse> {
        try {
            const response: AxiosResponse<LoginResponse> = await apiClient.postForm(AUTH_ENDPOINTS.LOGIN, credentials);

            if (response.data.success && response.data.data) {
                const { accessToken, refreshToken, user } = response.data.data;
                const decodedToken = JSON.parse(CryptoJs.enc.Utf8.stringify(CryptoJs.enc.Base64.parse(accessToken.split('.')[1])));
                const exp = decodedToken.exp;

                storeCookies('accessAdminToken', { accessToken, refreshToken, user, exp });
                storeCookies('user', user);

                if (typeof window !== 'undefined') {
                    localStorage.setItem('user', JSON.stringify(user));
                }
            }

            return response.data;
        } catch (error: any) {
            const apiError = error.response?.data;
            let message = apiError?.message || 'Login failed. Please try again.';

            if (apiError?.data?.errors) {
                const fieldErrors = Object.entries(apiError.data.errors)
                    .map(([field, err]) => `${field}: ${(err as string[]).join(', ')}`)
                    .join(' | ');
                message += ` (${fieldErrors})`;
            }

            // To maintain compatibility with the return type
            return {
                success: false,
                message: message,
                error: apiError?.error || 'Authentication error',
            };
        }
    },

    // Logout function
    async logout(): Promise<void> {
        try {
            await apiClient.post(AUTH_ENDPOINTS.LOGOUT);
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            clearAllCookies();
        }
    },

    // Get current user
    getCurrentUser(): User | null {
        if (typeof window === 'undefined') return null;

        try {
            const user = getCookies('user');
            if (user) {
                return user;
            }

            // Fallback to localStorage
            const userStorage = localStorage.getItem('user');
            if (userStorage) {
                return JSON.parse(userStorage);
            }
        } catch (error) {
            console.error('Error getting current user:', error);
        }

        return null;
    },

    // Check if user is authenticated
    isAuthenticated(): boolean {
        if (typeof window === 'undefined') return false;
        const token = getCookies('accessAdminToken');
        return !!token;
    },

    // Get auth token
    getToken(): string | null {
        if (typeof window === 'undefined') return null;

        try {
            const tokenData = getCookies('accessAdminToken');
            return tokenData?.accessToken || null;
        } catch (error) {
            console.error('Error getting token:', error);
        }

        return null;
    },

    // Get user profile
    async getProfile(): Promise<User> {
        try {
            const response = await apiClient.get(AUTH_ENDPOINTS.GET_PROFILE);
            return response.data.data;
        } catch (error: any) {
            const apiError = error.response?.data;
            let message = apiError?.message || 'Could not load profile';
            if (apiError?.data?.errors) {
                const fieldErrors = Object.entries(apiError.data.errors)
                    .map(([field, err]) => `${field}: ${(err as string[]).join(', ')}`)
                    .join(' | ');
                message += ` (${fieldErrors})`;
            }
            throw new Error(message);
        }
    },

    // Update user profile
    async updateProfile(profileData: FormData): Promise<User> {
        try {
            const response = await apiClient.put(AUTH_ENDPOINTS.UPDATE_PROFILE, profileData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response.data.data;
        } catch (error: any) {
            const apiError = error.response?.data;
            let message = apiError?.message || 'Could not update profile';
            if (apiError?.data?.errors) {
                const fieldErrors = Object.entries(apiError.data.errors)
                    .map(([field, err]) => `${field}: ${(err as string[]).join(', ')}`)
                    .join(' | ');
                message += ` (${fieldErrors})`;
            }
            throw new Error(message);
        }
    },

    // Change password
    async changePassword(currentPassword: string, password: string, confirmPassword: string): Promise<any> {
        try {
            const formData = new FormData();
            formData.append('currentPassword', currentPassword);
            formData.append('password', password);
            formData.append('confirmPassword', confirmPassword);
            formData.append('_method', 'PUT');
            const response = await apiClient.put(AUTH_ENDPOINTS.CHANGE_PASSWORD, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response.data;
        } catch (error: any) {
            const apiError = error.response?.data;
            let message = apiError?.message || 'Could not change password';
            if (apiError?.data?.errors) {
                const fieldErrors = Object.entries(apiError.data.errors)
                    .map(([field, err]) => `${field}: ${(err as string[]).join(', ')}`)
                    .join(' | ');
                message += ` (${fieldErrors})`;
            }
            throw new Error(message);
        }
    },
};

export default authService;
