'use client';
import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { authService, User, LoginRequest, LoginResponse } from '@/src/api/auth';
import { useRouter } from 'next/navigation';
import { storeCookies, getCookies } from '@/src/utils/cookies';

interface AuthContextType {
    user: User | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    login: (credentials: LoginRequest) => Promise<LoginResponse>;
    logout: () => Promise<void>;
    refreshUser: () => void;
    updateUser: (user: User) => void; // <-- Add this line
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
    children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
    const [user, setUser] = useState<User | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const router = useRouter();

    // Initialize auth state on mount
    useEffect(() => {
        initializeAuth();
    }, []);

    const initializeAuth = () => {
        try {
            const currentUser = authService.getCurrentUser();
            const isAuth = authService.isAuthenticated();

            if (isAuth && currentUser) {
                setUser(currentUser);
            } else {
                setUser(null);
            }
        } catch (error) {
            console.error('Error initializing auth:', error);
            setUser(null);
        } finally {
            setIsLoading(false);
        }
    };

    const login = async (credentials: LoginRequest): Promise<LoginResponse> => {
        setIsLoading(true);
        try {
            const response = await authService.login(credentials);

            if (response.success && response.data) {
                setUser(response.data.user);
                // Redirect to dashboard on successful login
                router.push('/dashboard');
            }

            return response;
        } catch (error) {
            console.error('Login error in context:', error);
            return {
                success: false,
                message: 'An unexpected error occurred',
                error: 'Context error',
            };
        } finally {
            setIsLoading(false);
        }
    };

    const logout = async (): Promise<void> => {
        setIsLoading(true);
        try {
            await authService.logout();
            setUser(null);
            router.push('/login');
        } catch (error) {
            console.error('Logout error in context:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const refreshUser = () => {
        const currentUser = authService.getCurrentUser();
        setUser(currentUser);
    };

    // Add this function to update user in context and persist to storage
    const updateUser = (updatedUser: User) => {
        setUser(updatedUser);

        // Also update the persistent storage (cookies and localStorage)
        if (typeof window !== 'undefined') {
            try {
                // Update localStorage
                localStorage.setItem('user', JSON.stringify(updatedUser));

                // Update cookies - we need to get the current token data and update the user part
                const currentTokenData = getCookies('accessAdminToken');
                if (currentTokenData) {
                    // Update the user data in the token cookie while preserving other data
                    const updatedTokenData = {
                        ...currentTokenData,
                        user: updatedUser
                    };
                    storeCookies('accessAdminToken', updatedTokenData);
                }

                // Update the user cookie - we need to preserve the exp from token data or set a default
                const userWithExp = {
                    ...updatedUser,
                    exp: currentTokenData?.exp || Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 days default
                };
                storeCookies('user', userWithExp);
            } catch (error) {
                console.error('Error updating user in storage:', error);
            }
        }
    };

    const value: AuthContextType = {
        user,
        isAuthenticated: !!user && authService.isAuthenticated(),
        isLoading,
        login,
        logout,
        refreshUser,
        updateUser,
    };

    return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

export default AuthContext;
