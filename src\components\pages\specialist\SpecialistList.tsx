'use client';

import React, { useState, useEffect, Fragment } from 'react';
import Link from 'next/link';
import { EyeIcon, PencilSquareIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import { Dialog, Transition } from '@headlessui/react';
import { getAllSpecialists, deleteSpecialist, Specialist } from '@/src/api/specialist';
import toast from 'react-hot-toast';
import Loading from '../../layouts/loading';
import Pagination from '../../Pagination';

const SpecialistList: React.FC = () => {
    const [search, setSearch] = useState('');
    const [specialists, setSpecialists] = useState<Specialist[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [showModal, setShowModal] = useState(false);
    const [selectedId, setSelectedId] = useState<number | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [totalItems, setTotalItems] = useState(0);
    const [totalPages, setTotalPages] = useState(1);
    const router = useRouter();

    useEffect(() => {
        const fetchSpecialists = async () => {
            try {
                setLoading(true);
                setError(null);
                const data = await getAllSpecialists();
                setSpecialists(data);
                setTotalItems(data.length);
                setTotalPages(Math.ceil(data.length / itemsPerPage));
                if (currentPage > Math.ceil(data.length / itemsPerPage) && data.length > 0) {
                    setCurrentPage(1);
                }
            } catch (err: any) {
                setError(err.message || 'An error occurred while fetching specialists');
            } finally {
                setLoading(false);
            }
        };
        fetchSpecialists();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        setTotalItems(filtered.length);
        setTotalPages(Math.ceil(filtered.length / itemsPerPage));
        if (currentPage > Math.ceil(filtered.length / itemsPerPage) && filtered.length > 0) {
            setCurrentPage(1);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [specialists, itemsPerPage, search]);

    const confirmDelete = (id: number) => {
        setSelectedId(id);
        setShowModal(true);
    };

    const handleDelete = async () => {
        if (selectedId === null) return;
        try {
            await deleteSpecialist(selectedId);
            setSpecialists((prev) => prev.filter((d) => d.id !== selectedId));
            toast.success('Specialist deleted successfully');
        } catch (err: any) {
            toast.error(err.message || 'An error occurred while deleting the specialist');
        } finally {
            setShowModal(false);
            setSelectedId(null);
        }
    };

    const handleView = (id: number) => router.push(`/specialist/${id}?mode=view`);
    const handleEdit = (id: number) => router.push(`/specialist/${id}?mode=edit`);

    const filtered = specialists.filter((d) => `${d.first_name} ${d.last_name} ${d.email} ${d.username}`.toLowerCase().includes(search.toLowerCase()));

    const getPaginatedData = () => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return filtered.slice(startIndex, endIndex);
    };

    if (loading) {
        return <Loading />;
    }

    return (
        <div className="p-4 bg-white rounded shadow relative">
            <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold">Specialists List</h2>
                <button
                    onClick={() => router.push('/specialist/add')}
                    className="border border-red-600 text-white px-5 py-1 rounded-full hover:bg-red-100  hover:text-[#f36e22] transition-colors duration-200 bg-[#eb6309]"
                >
                    Add Specialist
                </button>
            </div>
            <div className="mb-4 flex justify-end">
                <input type="text" placeholder="Search" value={search} onChange={(e) => setSearch(e.target.value)} className="form-input" style={{ marginLeft: 0, width: '33%', textAlign: 'left' }} />
            </div>
            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-100">
                        <tr>
                            <th className="py-3 px-4 text-start font-bold text-lg">First Name</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Last Name</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Email</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Username</th>
                            <th className="py-3 px-4 text-start font-bold text-lg">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {getPaginatedData().map((row: Specialist, idx: number) => (
                            <tr key={row.id} className={`hover:bg-gray-50 ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                                <td className="py-3 px-4 text-sm text-gray-800">{row.first_name}</td>
                                <td className="py-3 px-4 text-sm text-gray-800">{row.last_name}</td>
                                <td className="py-3 px-4 text-sm text-gray-800">{row.email}</td>
                                <td className="py-3 px-4 text-sm text-gray-800">{row.username}</td>
                                <td className="py-3 px-4 text-sm text-gray-800">
                                    <div className="flex gap-3 items-center">
                                        <button onClick={() => handleView(row.id)} title="View">
                                            <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
                                        </button>
                                        <button onClick={() => handleEdit(row.id)} title="Edit">
                                            <PencilSquareIcon className="h-5 w-5 text-green-500 hover:text-green-700" />
                                        </button>
                                        <button onClick={() => confirmDelete(row.id)} title="Delete">
                                            <TrashIcon className="h-5 w-5 text-red-500 hover:text-red-700" />
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <Pagination currentPage={currentPage} totalItems={filtered.length} itemsPerPage={itemsPerPage} onPageChange={setCurrentPage} onItemsPerPageChange={setItemsPerPage} />
            <Transition appear show={showModal} as={Fragment}>
                <Dialog as="div" className="relative z-50" onClose={() => setShowModal(false)}>
                    <Transition.Child as={Fragment} enter="ease-out duration-300" enterFrom="opacity-0" enterTo="opacity-100" leave="ease-in duration-200" leaveFrom="opacity-100" leaveTo="opacity-0">
                        <div className="fixed inset-0 bg-black bg-opacity-50" />
                    </Transition.Child>
                    <div className="fixed inset-0 overflow-y-auto">
                        <div className="flex min-h-full items-center justify-center p-4 text-center">
                            <Transition.Child
                                as={Fragment}
                                enter="ease-out duration-300"
                                enterFrom="opacity-0 scale-95"
                                enterTo="opacity-100 scale-100"
                                leave="ease-in duration-200"
                                leaveFrom="opacity-100 scale-100"
                                leaveTo="opacity-0 scale-95"
                            >
                                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                                        Confirm Deletion
                                    </Dialog.Title>
                                    <div className="mt-2 text-gray-600 text-sm">Are you sure you want to delete this specialist?</div>
                                    <div className="mt-4 flex justify-end gap-3">
                                        <button className="px-4 py-2 border rounded hover:bg-[#ebebeb] text-black" onClick={() => setShowModal(false)}>
                                            Cancel
                                        </button>
                                        <button className="px-4 py-2 bg-[#eb6309] text-white rounded hover:bg-[#fee2e2] hover:text-[#eb6309]" onClick={handleDelete}>
                                            Delete
                                        </button>
                                    </div>
                                </Dialog.Panel>
                            </Transition.Child>
                        </div>
                    </div>
                </Dialog>
            </Transition>
        </div>
    );
};

export default SpecialistList;
