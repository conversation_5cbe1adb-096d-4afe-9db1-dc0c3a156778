import apiClient from '@/src/utils/apiClient';
import { PATIENT_ENDPOINTS } from '@/src/utils/apiRoutes';
import { extractApiErrorMessage, extractApiResponseData, extractApiResponseArray } from '@/src/utils/apiErrorHandler';

export interface PatientDetails {
    id: number;
    doctor_id: number;
    first_name: string;
    last_name: string;
    email: string;
    dob: string;
    gender: string;
    ship_to_office: {
        id: number;
        doctor_id: number;
        clinic_name: string;
        street_address: string;
        city: string;
        postal_code: string;
        phone_number: string;
        created_at: string;
        updated_at: string;
    };
    bill_to_office: {
        id: number;
        doctor_id: number;
        clinic_name: string;
        street_address: string;
        city: string;
        postal_code: string;
        phone_number: string;
        created_at: string;
        updated_at: string;
    };
    plan_id: number;
    clinical_conditions: string;
    general_notes: string;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    stlFile1: string;
    stlFile2: string;
    cbctFile: string;
    profileRepose: string;
    buccalRight: string;
    buccalLeft: string;
    frontalRepose: string;
    frontalSmiling: string;
    labialAnterior: string;
    occlussalLower: string;
    occlussalUpper: string;
    radioGraph1: string;
    radioGraph2: string;
    data: {
        chief_complaint: string;
        treatment_goals: string;
        notes: string;
    };
    country: string;
    uuid: string | null;
    plan: {
        id: number;
        name: string;
        created_at: string;
        updated_at: string;
        type: string;
        duration_years: number | null;
        expiration_date: string | null;
    };
}

export interface PatientVersion {
    id: number;
    patient_id: number;
    created_by: number;
    version_number: number;
    title: string;
    status: 'sent_by_doctor' | 'approved_by_specialist' | 'rejected_by_specialist' | 'approved_by_doctor' | 'cancelled_by_admin';
    rejection_reason?: string | null;
    approval_reason?: string | null;
    upper_steps?: number | null;
    lower_steps?: number | null;
    is_latest_version: boolean;
    data: any;
    created_at: string;
    shared_link?: string | null;
    specialist_id?: number | null;
}

export interface Patient {
    id: number;
    doctor_id: number;
    first_name: string;
    last_name: string;
    email?: string | null;
    dob: string;
    gender: string;
    ship_to_office: string;
    bill_to_office: string;
    plan_id: number;
    clinical_conditions?: string | null;
    general_notes?: string | null;
    is_active: boolean;
    country: string;
    uuid: string;
    doctor_first_name: string;
    doctor_last_name: string;
    versions: PatientVersion[];
    created_at: string;
    updated_at: string;
    // File URLs
    stlFile1?: string | null;
    stlFile2?: string | null;
    cbctFile?: string | null;
    profileRepose?: string | null;
    buccalRight?: string | null;
    buccalLeft?: string | null;
    frontalRepose?: string | null;
    frontalSmiling?: string | null;
    labialAnterior?: string | null;
    occlussalLower?: string | null;
    occlussalUpper?: string | null;
    radioGraph1?: string | null;
    radioGraph2?: string | null;
    data?: any;
}

// List all patients
export const getAllPatients = async (): Promise<Patient[]> => {
    try {
        const response = await apiClient.get(PATIENT_ENDPOINTS.GET_PATIENTS);
        return extractApiResponseArray<Patient>(response, 'Unexpected response structure');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not load patients'));
    }
};

// View one patient by ID
export const viewPatient = async (patientId: number): Promise<PatientDetails> => {
    try {
        const response = await apiClient.get(PATIENT_ENDPOINTS.GET_PATIENT(patientId.toString()));
        return extractApiResponseData<PatientDetails>(response, 'No patient data received');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not fetch patient'));
    }
};

// Update patient status
export const updatePatientStatus = async (patientId: number, isActive: boolean): Promise<Patient> => {
    try {
        const response = await apiClient.patch(PATIENT_ENDPOINTS.UPDATE_PATIENT_STATUS(patientId.toString()), {
            is_active: isActive,
        });
        return extractApiResponseData<Patient>(response, 'No patient data received');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not update patient status'));
    }
};

// Update patient version status (for latest version)
export const updatePatientVersionStatus = async (patientId: number, versionId: number, isActive: boolean): Promise<PatientVersion> => {
    try {
        const response = await apiClient.patch(PATIENT_ENDPOINTS.UPDATE_PATIENT_VERSION_STATUS(patientId.toString(), versionId.toString()), {
            is_active: isActive,
        });
        return extractApiResponseData<PatientVersion>(response, 'No patient version data received');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not update patient version status'));
    }
};
