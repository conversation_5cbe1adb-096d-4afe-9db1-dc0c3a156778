'use client';
import Image from 'next/image';
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/src/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Loading from '@/src/components/layouts/loading';
import toast from 'react-hot-toast';

const LoginPage = () => {
    const [formData, setFormData] = useState({
        email: '', // Start with empty email
        password: '', // Start with empty password
    });
    const [error, setError] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    const { login, isAuthenticated, isLoading } = useAuth();
    const router = useRouter();
    const [checking, setchecking] = useState(true);

    // Redirect if already authenticated
    useEffect(() => {
        if (isAuthenticated && !isLoading) {
            router.push('/dashboard');
            return;
        }
        setchecking(false);
    }, [isAuthenticated, isLoading, router]);

    // Show loading state while checking authentication, submitting, or loading
    if (checking || isSubmitting || isLoading || isAuthenticated) return <Loading />;

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
        // Clear error when user starts typing
        if (error) setError('');
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');
        setIsSubmitting(true);

        try {
            const response = await login(formData);

            if (!response.success) {
                toast.error(response.message || 'Login failed. Please try again.');
                setError(response.message || 'Login failed. Please try again.');
            }
        } catch (err) {
            console.error('Login error:', err);
            toast.error('An unexpected error occurred. Please try again.');
            setError('An unexpected error occurred. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    // Show loading state while checking authentication
    if (isLoading || isSubmitting) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-100">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[rgb(235,99,9)] mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100 flex-col bg-cover bg-no-repeat bg-center" style={{ backgroundImage: "url('/assets/images/BGauth.png')" }}>
            {/* Logo Section */}
            <div className="mb-5">
                <Image src="/assets/images/logo.png" alt="Logo" className="mx-auto w-[240px] mb-5" width={240} height={240} priority />
            </div>

            <div className="bg-white p-8 rounded shadow-md w-full max-w-sm text-center">
                {/* Welcome Heading */}
                <h1 className="text-[38px] leading-[45px] font-bold text-gray-700 mb-5">Welcome to Dashboard</h1>

                <h2 className="text-2xl font-semibold mb-6 text-[rgb(235,99,9)]">Login</h2>

                {/* Error Message */}
                {/* {error && <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded text-sm">{error}</div>} */}

                <form onSubmit={handleSubmit} className="space-y-4 text-left">
                    <div>
                        <label className="block text-gray-700 mb-1">Email</label>
                        <input
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            placeholder="Enter email"
                            required
                            disabled={isSubmitting}
                            className="w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-[rgb(235,99,9)] disabled:bg-gray-100 disabled:cursor-not-allowed"
                        />
                    </div>

                    <div>
                        <label className="block text-gray-700 mb-1">Password</label>
                        <input
                            type="password"
                            name="password"
                            value={formData.password}
                            onChange={handleInputChange}
                            placeholder="Enter password"
                            required
                            disabled={isSubmitting}
                            className="w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-[rgb(235,99,9)] disabled:bg-gray-100 disabled:cursor-not-allowed"
                        />
                    </div>

                    <button
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full bg-[rgb(235,99,9)] text-white py-2 rounded hover:bg-orange-700 transition duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                        {isSubmitting ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                Signing in...
                            </>
                        ) : (
                            'Login'
                        )}
                    </button>
                </form>
            </div>
        </div>
    );
};

export default LoginPage;
