import Image from 'next/image';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { authService, User } from '@/src/api/auth';
import toast from 'react-hot-toast';
import Loading from '@/src/components/layouts/loading';
import { baseUrl } from '@/src/utils/apiRoutes';
import { useAuth } from '@/src/contexts/AuthContext';

// ChangePasswordForm component
const ChangePasswordForm = () => {
    const [currentPassword, setCurrentPassword] = useState('');
    const [password, setPassword] = useState('');
    const [passwordConfirmation, setPasswordConfirmation] = useState('');
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const [loading, setLoading] = useState(false);

    const validate = () => {
        const newErrors: { [key: string]: string } = {};
        if (!currentPassword) newErrors.currentPassword = 'Current password is required';
        if (!password) newErrors.password = 'New password is required';
        if (!passwordConfirmation) newErrors.passwordConfirmation = 'Please confirm your new password';
        if (password && passwordConfirmation && password !== passwordConfirmation) newErrors.passwordConfirmation = 'Passwords do not match';
        return newErrors;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        const validationErrors = validate();
        setErrors(validationErrors);
        if (Object.keys(validationErrors).length > 0) return;
        setLoading(true);
        try {
            await authService.changePassword(currentPassword, password, passwordConfirmation);
            toast.success('Password changed successfully!');
            setCurrentPassword('');
            setPassword('');
            setPasswordConfirmation('');
        } catch (err: any) {
            toast.error(err.message || 'Failed to change password');
        } finally {
            setLoading(false);
        }
    };

    return (
        <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-md max-w-lg">
            <div className="mb-4">
                <label htmlFor="currentPassword" className="font-medium">
                    Current Password:
                </label>
                <input
                    id="currentPassword"
                    name="currentPassword"
                    type="password"
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    className="border p-2 w-full rounded-md mt-1"
                />
                {errors.currentPassword && <div className="text-red-500 text-sm mt-1">{errors.currentPassword}</div>}
            </div>
            <div className="mb-4">
                <label htmlFor="password" className="font-medium">
                    New Password:
                </label>
                <input id="password" name="password" type="password" value={password} onChange={(e) => setPassword(e.target.value)} className="border p-2 w-full rounded-md mt-1" />
                {errors.password && <div className="text-red-500 text-sm mt-1">{errors.password}</div>}
            </div>
            <div className="mb-6">
                <label htmlFor="passwordConfirmation" className="font-medium">
                    Confirm New Password:
                </label>
                <input
                    id="passwordConfirmation"
                    name="passwordConfirmation"
                    type="password"
                    value={passwordConfirmation}
                    onChange={(e) => setPasswordConfirmation(e.target.value)}
                    className="border p-2 w-full rounded-md mt-1"
                />
                {errors.passwordConfirmation && <div className="text-red-500 text-sm mt-1">{errors.passwordConfirmation}</div>}
            </div>
            <button type="submit" className="bg-[#ef7125] text-white px-6 py-2 rounded-full hover:bg-[#fee2e2] hover:text-[#ef7125] transition-colors duration-200" disabled={loading}>
                {loading ? 'Saving...' : 'Change Password'}
            </button>
        </form>
    );
};

const ProfilePage = () => {
    const router = useRouter();
    const { refreshUser, updateUser: contextUpdateUser } = useAuth();
    const [profile, setProfile] = useState<Partial<User>>({});
    const [loading, setLoading] = useState(true);
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const [imageError, setImageError] = useState(false);

    // Helper function to construct proper image URL
    const getImageUrl = (imagePath: string): string => {
        if (!imagePath) return '';
        
        // If it's already a full URL or starts with /, return as is
        if (imagePath.startsWith('http') || imagePath.startsWith('https') || imagePath.startsWith('/')) {
            return imagePath;
        }
        
        // If it's a blob URL (for preview), return as is
        if (imagePath.startsWith('blob:')) {
            return imagePath;
        }
        
        // For relative paths, construct full URL
        const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
        const cleanImagePath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
        
        console.log('Base URL:', cleanBaseUrl);
        console.log('Image Path:', imagePath);
        console.log('Final URL:', `${cleanBaseUrl}${cleanImagePath}`);
        
        return `${cleanBaseUrl}${cleanImagePath}`;
    };
    useEffect(() => {
        const fetchProfile = async () => {
            try {
                const data = await authService.getProfile();
                setProfile(data);
            } catch (err: any) {
                toast.error(err.message || 'Failed to load profile');
            } finally {
                setLoading(false);
            }
        };
        fetchProfile();
    }, []);

    const validate = (fields = profile, image = imageFile) => {
        const newErrors: { [key: string]: string } = {};
        if (!fields.first_name) newErrors.first_name = 'First name is required';
        if (!fields.last_name) newErrors.last_name = 'Last name is required';
        if (!fields.email) newErrors.email = 'Email is required';
        if (!fields.profile_image && !image) newErrors.profile_image = 'Profile image is required';
        return newErrors;
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setProfile((prev) => {
            const updated = { ...prev, [name]: value };
            setErrors(validate(updated, imageFile));
            return updated;
        });
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setImageFile(file);
            setImageError(false); // Reset image error when new file is selected
            const previewUrl = URL.createObjectURL(file);
            setProfile((prev) => {
                const updated = { ...prev, profile_image: previewUrl };
                setErrors(validate(updated, file));
                return updated;
            });
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        const validationErrors = validate(profile, imageFile);
        setErrors(validationErrors);
        if (Object.keys(validationErrors).length > 0) {
            setLoading(false);
            return;
        }
        setLoading(true);
        try {
            const formData = new FormData();
            Object.entries(profile).forEach(([key, value]) => {
                if (key !== 'profile_image' && value) {
                    formData.append(key, value as string);
                }
            });
            if (imageFile) {
                formData.append('profile_image', imageFile);
            }
            formData.append('_method', 'PUT');
            const updatedUser = await authService.updateProfile(formData);
            toast.success('Profile updated successfully!');
            contextUpdateUser(updatedUser);
            setProfile(updatedUser); // Update local profile state
            setImageFile(null); // Clear the selected file
            setImageError(false); // Reset image error state
        } catch (err: any) {
            toast.error(err.message || 'Failed to update profile');
        } finally {
            setLoading(false);
        }
    };

    if (loading) return <Loading />;

    return (
        <>
            <h2 className="text-2xl font-bold mb-4">Edit Profile</h2>
            <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-md">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label htmlFor="first_name" className="font-medium">
                            First Name:
                        </label>
                        <input id="first_name" name="first_name" value={profile.first_name || ''} onChange={handleInputChange} className="border p-2 w-full rounded-md mt-1" />
                        {errors.first_name && <div className="text-red-500 text-sm mt-1">{errors.first_name}</div>}
                    </div>
                    <div>
                        <label htmlFor="last_name" className="font-medium">
                            Last Name:
                        </label>
                        <input id="last_name" name="last_name" value={profile.last_name || ''} onChange={handleInputChange} className="border p-2 w-full rounded-md mt-1" />
                        {errors.last_name && <div className="text-red-500 text-sm mt-1">{errors.last_name}</div>}
                    </div>
                    <div>
                        <label htmlFor="email" className="font-medium">
                            Email:
                        </label>
                        <input id="email" name="email" type="email" value={profile.email || ''} onChange={handleInputChange} className="border p-2 w-full rounded-md mt-1" />
                        {errors.email && <div className="text-red-500 text-sm mt-1">{errors.email}</div>}
                    </div>
                    <div className="col-span-1 md:col-span-2">
                        <label htmlFor="profile_image" className="font-medium">
                            Profile Image:
                        </label>
                        <div className="flex items-center gap-4 mt-1">
                            {profile.profile_image && !imageError ? (
                                <Image
                                    src={getImageUrl(profile.profile_image)}
                                    alt="Profile"
                                    width={96}
                                    height={96}
                                    className="h-24 w-24 rounded-full object-cover"
                                    unoptimized={profile.profile_image.startsWith('blob:')}
                                    onError={() => {
                                        console.log('Image failed to load:', profile.profile_image);
                                        setImageError(true);
                                    }}
                                />
                            ) : (
                                <div className="h-24 w-24 rounded-full bg-gray-200 flex items-center justify-center">
                                    <svg className="h-12 w-12 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                                    </svg>
                                </div>
                            )}
                            <input id="profile_image" name="profile_image" type="file" onChange={handleFileChange} className="border p-2 w-full rounded-md" />
                            {errors.profile_image && <div className="text-red-500 text-sm mt-1">{errors.profile_image}</div>}
                        </div>
                    </div>
                </div>
                <div className="flex gap-4 mt-6">
                    <button type="submit" className="bg-[#ef7125] text-white px-6 py-2 rounded-full hover:bg-[#fee2e2] hover:text-[#ef7125] transition-colors duration-200" disabled={loading}>
                        {loading ? 'Saving...' : 'Save Changes'}
                    </button>
                    <button type="button" onClick={() => router.back()} className="border border-gray-400 text-gray-700 px-6 py-2 rounded-full hover:bg-gray-100 transition-colors duration-200">
                        Cancel
                    </button>
                </div>
            </form>
            {/* Change Password Form */}
            <div className="mt-10">
                <h2 className="text-2xl font-bold mb-4">Change Password</h2>
                <ChangePasswordForm />
            </div>
        </>
    );
};

export default ProfilePage;
