import apiClient from '@/src/utils/apiClient';
import { DOCTOR_ENDPOINTS } from '@/src/utils/apiRoutes';
import { DASHBOARD_ENDPOINTS } from '@/src/utils/apiRoutes';
import { extractApiErrorMessage, extractApiResponseData, extractApiResponseArray } from '@/src/utils/apiErrorHandler';

export interface Doctor {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    doctor_addresses?: string;
    created_at: string;
    updated_at: string;
}

export interface CreateDoctorInput {
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    doctor_addresses?: string;
}

// List doctors
export const getAllDoctors = async (): Promise<Doctor[]> => {
    try {
        const response = await apiClient.get(DOCTOR_ENDPOINTS.GET_DOCTORS);
        return extractApiResponseArray<Doctor>(response, 'Unexpected response structure');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not load doctors'));
    }
};

// Create doctor
export const createDoctor = async (doctorData: CreateDoctorInput): Promise<Doctor> => {
    try {
        const response = await apiClient.post(DOCTOR_ENDPOINTS.CREATE_DOCTOR, doctorData);
        return extractApiResponseData<Doctor>(response, 'No data returned after creation');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not create doctor'));
    }
};

// Update doctor
export const updateDoctor = async (doctorId: number, doctorData: Partial<CreateDoctorInput>): Promise<Doctor> => {
    try {
        const response = await apiClient.patch(DOCTOR_ENDPOINTS.UPDATE_DOCTOR(doctorId.toString()), doctorData);
        return extractApiResponseData<Doctor>(response, 'No data returned after update');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not update doctor'));
    }
};

// View one doctor
export const viewDoctor = async (doctorId: number): Promise<Doctor> => {
    try {
        const response = await apiClient.get(DOCTOR_ENDPOINTS.GET_DOCTOR(doctorId.toString()));
        return extractApiResponseData<Doctor>(response, 'No doctor data received');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not fetch doctor'));
    }
};

// Delete doctor
export const deleteDoctor = async (doctorId: number): Promise<void> => {
    try {
        await apiClient.delete(DOCTOR_ENDPOINTS.DELETE_DOCTOR(doctorId.toString()));
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not delete doctor'));
    }
};

// Dashboard stats
export interface DashboardStats {
    doctors: number;
    patients: number;
    specialist: number;
    pendingPatients: number;
}

export const getDashboardStats = async (): Promise<DashboardStats> => {
    try {
        const response = await apiClient.get(DASHBOARD_ENDPOINTS.GET_STATS);
        // The API returns { status, success, data, message }
        if (response.data && response.data.data) {
            return response.data.data as DashboardStats;
        }
        throw new Error('Unexpected response structure');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not fetch dashboard stats'));
    }
};

// Doctors Patient Count
export interface DoctorPatientCount {
    doctor_id: number;
    doctor_name: string;
    patient_count: number;
}

export const getDoctorsPatientCount = async (): Promise<DoctorPatientCount[]> => {
    try {
        const response = await apiClient.get(DASHBOARD_ENDPOINTS.DOCTORS_PATIENT_COUNT);
        if (response.data && response.data.data) {
            // Ensure patient_count is a number
            return response.data.data.map((item: any) => ({
                doctor_id: item.doctor_id,
                doctor_name: item.doctor_name,
                patient_count: Number(item.patient_count),
            }));
        }
        throw new Error('Unexpected response structure');
    } catch (error: any) {
        throw new Error(extractApiErrorMessage(error, 'Could not fetch doctors patient count'));
    }
};
