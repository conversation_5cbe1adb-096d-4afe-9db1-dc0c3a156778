/**
 * Standardized API error handling utilities
 */

export interface ApiErrorResponse {
    message?: string;
    data?: {
        errors?: Record<string, string[] | { _errors: string[] }>;
    };
    error?: string;
}

/**
 * Standardized error message extraction from API responses
 * @param error - The error object from axios
 * @param defaultMessage - Default message if no specific error is found
 * @returns Formatted error message
 */
export const extractApiErrorMessage = (error: any, defaultMessage: string): string => {
    const apiError: ApiErrorResponse = error.response?.data;
    let message = apiError?.message || defaultMessage;

    // Handle field validation errors
    if (apiError?.data?.errors) {
        const fieldErrors = Object.entries(apiError.data.errors)
            .map(([field, err]) => {
                if (Array.isArray(err)) {
                    return `${field}: ${err.join(', ')}`;
                } else if (typeof err === 'object' && err !== null && '_errors' in err && Array.isArray((err as any)._errors)) {
                    return `${field}: ${(err as any)._errors.join(', ')}`;
                }
                return `${field}: ${JSON.stringify(err)}`;
            })
            .join(' | ');
        message += ` (${fieldErrors})`;
    }

    return message;
};

/**
 * Standardized API response data extraction
 * @param response - The axios response object
 * @param errorMessage - Error message to throw if data is not found
 * @returns The extracted data
 */
export const extractApiResponseData = <T>(response: any, errorMessage: string): T => {
    const { data } = response.data;
    if (!data) {
        throw new Error(errorMessage);
    }
    return data;
};

/**
 * Standardized API response array extraction
 * @param response - The axios response object
 * @param errorMessage - Error message to throw if data is not an array
 * @returns The extracted array data
 */
export const extractApiResponseArray = <T>(response: any, errorMessage: string): T[] => {
    const { data } = response.data;
    if (!Array.isArray(data)) {
        throw new Error(errorMessage);
    }
    return data;
};

/**
 * Handle special case for plans API response structure
 * @param response - The axios response object
 * @returns The extracted plans array
 */
export const extractPlansResponseArray = (response: any): any[] => {
    const raw = response.data;
    const plansArray = raw?.data?.plans;

    if (raw.message?.includes('fetched') && Array.isArray(plansArray)) {
        return plansArray;
    } else {
        throw new Error('Unexpected response structure');
    }
};
