'use client';
import { useDispatch, useSelector } from 'react-redux';
import Link from 'next/link';
import Image from 'next/image';
import { IRootState } from '@/src/store';
import { toggleTheme, toggleSidebar, toggleRTL } from '@/src/store/themeConfigSlice';
import { useAuth } from '@/src/contexts/AuthContext';
import Dropdown from '@/src/components/dropdown';
import IconMenu from '@/src/components/icon/icon-menu';
import IconBellBing from '@/src/components/icon/icon-bell-bing';
import IconUser from '@/src/components/icon/icon-user';
import IconLogout from '@/src/components/icon/icon-logout';
import IconMenuDashboard from '@/src/components/icon/menu/icon-menu-dashboard';
import IconCaretDown from '@/src/components/icon/icon-caret-down';
import { useRouter } from 'next/navigation';
import { baseUrl } from '@/src/utils/apiRoutes';
import { getTranslation } from '@/i18n';
import { use, useEffect, useState } from 'react';
import authService, { User } from '@/src/api/auth';

const Header = () => {
    const dispatch = useDispatch();
    const router = useRouter();
    const { t, i18n } = getTranslation();
    const { user, logout, isLoading } = useAuth(); // Remove updateUser from destructuring

    console.log('user', user);
    // Remove local profile state and effect
    // const [profile, setProfile] = useState<User | undefined>();
    // useEffect(() => {
    //   const fetchProfile = async () => {
    //     try {
    //       const profiledata = await authService.getProfile();
    //       setProfile(profiledata);
    //       updateUser(profiledata);
    //     } catch (error) {
    //       console.error('Failed to fetch profile:', error);
    //     }
    //   };
    //   fetchProfile();
    // }, []);

    // Use user from context for profile image and info
    const getProfileImageUrl = () => {
        if (!user?.profile_image) {
            return '/assets/images/user-profile.jpeg';
        }

        // If it's already an absolute URL, use as is with cache busting
        if (user.profile_image.startsWith('http')) {
            const separator = user.profile_image.includes('?') ? '&' : '?';
            return `${user.profile_image}${separator}t=${Date.now()}`;
        }

        // If it's a blob URL (for preview), return as is
        if (user.profile_image.startsWith('blob:')) {
            return user.profile_image;
        }

        // If it starts with /, it's likely a static asset, use as is with cache busting
        if (user.profile_image.startsWith('/assets/')) {
            const separator = user.profile_image.includes('?') ? '&' : '?';
            return `${user.profile_image}${separator}t=${Date.now()}`;
        }

        // For relative paths (uploaded images), construct full URL with baseUrl
        const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
        const cleanImagePath = user.profile_image.startsWith('/') ? user.profile_image : `/${user.profile_image}`;
        return `${cleanBaseUrl}${cleanImagePath}?t=${Date.now()}`;
    };

    const profileImageUrl = getProfileImageUrl();

    console.log('profileImageUrl', profileImageUrl);

    const isRtl = useSelector((state: IRootState) => state.themeConfig.rtlClass) === 'rtl';
    const themeConfig = useSelector((state: IRootState) => state.themeConfig);

    // Handle logout
    const handleLogout = async () => {
        try {
            await logout();
        } catch (error) {
            console.error('Logout error:', error);
        }
    };

    const setLocale = (flag: string) => {
        if (flag.toLowerCase() === 'ae') {
            dispatch(toggleRTL('rtl'));
        } else {
            dispatch(toggleRTL('ltr'));
        }
        router.refresh();
    };

    const [search, setSearch] = useState(false);

    return (
        <header className={`z-40 ${themeConfig.semidark && themeConfig.menu === 'horizontal' ? 'dark' : ''}`}>
            <div className="shadow-sm">
                <div className="relative flex w-full items-center bg-white px-5 py-4 dark:bg-black">
                    <div className="horizontal-logo flex items-center justify-between ltr:mr-2 rtl:ml-2 lg:hidden">
                        <Link href="/" className="main-logo flex shrink-0 items-center">
                            <Image className="inline w-[180px] ltr:-ml-1 rtl:-mr-1" src="/assets/images/logo.png" alt="logo" width={180} height={180} priority />
                        </Link>
                        <button
                            title="Open Sidebar"
                            type="button"
                            className="collapse-icon flex flex-none rounded-full bg-white-light/40 p-2 hover:bg-white-light/90 hover:text-primary ltr:ml-2 rtl:mr-2 dark:bg-dark/40 dark:text-[#d0d2d6] dark:hover:bg-dark/60 dark:hover:text-primary lg:hidden"
                            onClick={() => dispatch(toggleSidebar())}
                        >
                            <IconMenu className="h-5 w-5" />
                        </button>
                    </div>

                    <div className="flex items-center space-x-1.5 ltr:ml-auto rtl:mr-auto rtl:space-x-reverse dark:text-[#d0d2d6] sm:flex-1 ltr:sm:ml-0 sm:rtl:mr-0 lg:space-x-2">
                        <div className="sm:ltr:mr-auto sm:rtl:ml-auto">{/* Search form removed for simplicity */}</div>

                        <div className="dropdown flex shrink-0">
                            <Dropdown
                                offset={[0, 8]}
                                placement={`${isRtl ? 'bottom-start' : 'bottom-end'}`}
                                btnClassName="relative group block"
                                button={
                                    <Image
                                        key={`header-profile-${user?.id}-${user?.profile_image}`}
                                        className="h-9 w-9 rounded-full object-cover saturate-50 group-hover:saturate-100"
                                        src={profileImageUrl}
                                        alt="userProfile"
                                        width={36}
                                        height={36}
                                        priority
                                        unoptimized={profileImageUrl.startsWith(baseUrl) || profileImageUrl.startsWith('blob:')}
                                    />
                                }
                            >
                                <ul className="w-[230px] !py-0 font-semibold text-dark dark:text-white-dark dark:text-white-light/90">
                                    <li>
                                        <div className="flex items-center px-4 py-4">
                                            <Image
                                                key={`dropdown-profile-${user?.id}-${user?.profile_image}`}
                                                className="h-10 w-10 rounded-md object-cover"
                                                src={profileImageUrl}
                                                alt="userProfile"
                                                width={40}
                                                height={40}
                                                priority
                                                unoptimized={profileImageUrl.startsWith(baseUrl) || profileImageUrl.startsWith('blob:')}
                                            />
                                            <div className="truncate ltr:pl-4 rtl:pr-4">
                                                <h4 className="text-base">
                                                    {user?.first_name || user?.last_name ? `${user?.first_name ?? ''} ${user?.last_name ?? ''}`.trim() : 'Admin User'}
                                                    {/* <span className="rounded bg-success-light px-1 text-xs text-success ltr:ml-2 rtl:ml-2">
                                                        {user?.role ? user.role.charAt(0).toUpperCase() + user.role.slice(1) : 'Admin'}
                                                    </span> */}
                                                </h4>
                                                <span className="text-black/60 dark:text-dark-light/60">{user?.email || '<EMAIL>'}</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li>
                                        <Link
                                            href="/profile"
                                            className="flex items-center px-4 py-2 !text-[#f47022] hover:!bg-[#f47022] hover:!text-white dark:hover:!bg-[#f47022] dark:hover:!text-white transition-colors duration-200"
                                        >
                                            <IconUser className="w-4 h-4 mr-2" />
                                            Profile
                                        </Link>
                                    </li>
                                    <li className="border-t border-white-light dark:border-white-light/10">
                                        <button
                                            type="button"
                                            onClick={handleLogout}
                                            disabled={isLoading}
                                            className="!py-3 w-full text-left flex items-center !text-[#f47022] hover:!bg-[#f47022] hover:!text-white dark:hover:!bg-[#f47022] dark:hover:!text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                                        >
                                            <IconLogout className="h-4.5 w-4.5 shrink-0 rotate-90 ltr:mr-2 rtl:ml-2" />
                                            {isLoading ? 'Signing Out...' : 'Sign Out'}
                                        </button>
                                    </li>
                                </ul>
                            </Dropdown>
                        </div>
                    </div>
                </div>

                {/* horizontal menu */}
                <ul className="horizontal-menu hidden border-t border-[#ebedf2] bg-white px-6 py-1.5 font-semibold text-black rtl:space-x-reverse dark:border-[#191e3a] dark:bg-black dark:text-white-dark lg:space-x-1.5 xl:space-x-8">
                    <li className="menu nav-item relative">
                        <button type="button" className="nav-link">
                            <div className="flex items-center">
                                <IconMenuDashboard className="shrink-0" />
                                <span className="px-1">{t('dashboard')}</span>
                            </div>
                            <div className="right_arrow">
                                <IconCaretDown />
                            </div>
                        </button>
                        <ul className="sub-menu">
                            <li>
                                <Link href="/">{t('sales')}</Link>
                            </li>
                            <li>
                                <Link href="/analytics">{t('analytics')}</Link>
                            </li>
                            <li>
                                <Link href="/finance">{t('finance')}</Link>
                            </li>
                            <li>
                                <Link href="/crypto">{t('crypto')}</Link>
                            </li>
                        </ul>
                    </li>
                    {/* Removed Apps, Components, Tables, Forms, Pages, and More menus */}
                </ul>
            </div>
        </header>
    );
};

export default Header;
