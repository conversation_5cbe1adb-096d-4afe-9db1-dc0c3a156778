'use client';

import React, { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import ApexCharts from 'apexcharts';
import { getDashboardStats, DashboardStats, getDoctorsPatientCount, DoctorPatientCount } from '@/src/api/doctor';
let lastDate = new Date().getTime();
const XAXIS_RANGE = 60000; // 60 seconds

// Utility Functions
const generateDataPoint = () => {
    lastDate += 1000;
    return [lastDate, Math.floor(Math.random() * 80) + 10] as [number, number];
};

const getInitialData = (): [number, number][] => {
    const init: [number, number][] = [];
    for (let i = 0; i < 60; i++) {
        init.push(generateDataPoint());
    }
    return init;
};

const DashboardCharts = () => {
    const [areaSeries] = useState([
        {
            name: 'Appointments',
            data: [12, 18, 20, 25, 22, 30, 28, 35, 40, 38, 42, 45],
        },
        {
            name: 'Treatments',
            data: [5, 8, 10, 12, 15, 18, 20, 22, 25, 28, 30, 33],
        },
    ]);

    const areaOptions: ApexOptions = {
        chart: { height: 350, type: 'area', dropShadow: { enabled: true, top: 3, left: 2, blur: 4, opacity: 0.15 } },
        colors: ['#EB6309', '#1E90FF'],
        dataLabels: { enabled: false },
        stroke: { curve: 'smooth' },
        xaxis: { categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] },
        tooltip: {
            x: { formatter: (val: string | number): string => String(val) },
        },
    };

    // Stats Cards State
    const [statsData, setStatsData] = useState<DashboardStats>({ doctors: 0, patients: 0, specialist: 0, pendingPatients: 0 });
    const [statsLoading, setStatsLoading] = useState<boolean>(true);
    const [statsError, setStatsError] = useState<string | null>(null);

    // Donut Chart
    const [donutSeries, setDonutSeries] = useState<number[]>([0, 0, 0]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        // Fetch dashboard stats for both stats cards and donut chart
        setStatsLoading(true);
        getDashboardStats()
            .then((stats: DashboardStats) => {
                setStatsData(stats);
                setDonutSeries([stats.doctors, stats.specialist, stats.patients]);
                setLoading(false);
                setStatsLoading(false);
            })
            .catch((err) => {
                const errorMessage = err.message || 'Failed to load dashboard stats';
                setError(errorMessage);
                setStatsError(errorMessage);
                setLoading(false);
                setStatsLoading(false);
            });
    }, []);

    const donutOptions: ApexOptions = {
        chart: { type: 'donut' },
        labels: ['Doctors', 'Specialists', 'Patients'],
        colors: ['#EB6309', '#1E90FF', '#22C55E'],
        legend: { position: 'bottom' },
        dataLabels: { enabled: false },
        responsive: [
            {
                breakpoint: 480,
                options: {
                    chart: { width: 280 },
                    legend: { position: 'bottom' },
                },
            },
        ],
    };

    // Bar Chart for Doctors and Patient Counts (API Data)
    const [doctorBarSeries, setDoctorBarSeries] = useState<any[]>([]);
    const [doctorBarCategories, setDoctorBarCategories] = useState<string[]>([]);
    const [doctorBarLoading, setDoctorBarLoading] = useState<boolean>(true);
    const [doctorBarError, setDoctorBarError] = useState<string | null>(null);

    useEffect(() => {
        setDoctorBarLoading(true);
        getDoctorsPatientCount()
            .then((data: DoctorPatientCount[]) => {
                setDoctorBarCategories(data.map((item) => item.doctor_name));
                setDoctorBarSeries([
                    {
                        name: 'Patients',
                        data: data.map((item) => item.patient_count),
                    },
                ]);
                setDoctorBarLoading(false);
            })
            .catch((err) => {
                setDoctorBarError(err.message || 'Failed to load doctor patient count');
                setDoctorBarLoading(false);
            });
    }, []);

    const doctorBarOptions: ApexOptions = {
        chart: {
            type: 'bar',
            height: 350,
        },
        plotOptions: {
            bar: {
                borderRadius: 4,
                horizontal: false,
                columnWidth: '50%',
            },
        },
        dataLabels: {
            enabled: true,
        },
        xaxis: {
            categories: doctorBarCategories,
            title: {
                text: 'Doctors',
            },
        },
        yaxis: {
            title: {
                text: 'Number of Patients',
            },
        },
        colors: ['#1E90FF'],
    };

    return (
        <>
            <h2 className="text-2xl font-bold mb-6">Dashboard</h2>

            {/* Stats Cards Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {/* Total Patients Card */}
                <div className="bg-white rounded-2xl p-6 shadow-sm border border-red-500 hover:shadow-md transition-shadow">
                    <div className="flex flex-col items-center text-center">
                        <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center mb-4">
                            <svg className="w-6 h-6 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                            </svg>
                        </div>
                        <p className="text-3xl font-bold text-gray-900 mb-2">{statsLoading ? '...' : statsError ? '0' : statsData.patients}</p>
                        <p className="text-sm text-gray-600 font-medium">Total Patients</p>
                    </div>
                </div>

                {/* Total Doctors Card */}
                <div className="bg-white rounded-2xl p-6 shadow-sm border border-indigo-500 hover:shadow-md transition-shadow">
                    <div className="flex flex-col items-center text-center">
                        <div className="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center mb-4">
                            <svg className="w-6 h-6 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <p className="text-3xl font-bold text-gray-900 mb-2">{statsLoading ? '...' : statsError ? '0' : statsData.doctors}</p>
                        <p className="text-sm text-gray-600 font-medium">Total Doctors</p>
                    </div>
                </div>

                {/* Total Specialists Card */}
                <div className="bg-white rounded-2xl p-6 shadow-sm border border-green-500 hover:shadow-md transition-shadow">
                    <div className="flex flex-col items-center text-center">
                        <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-4">
                            <svg className="w-6 h-6 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    fillRule="evenodd"
                                    d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                                    clipRule="evenodd"
                                />
                                <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
                            </svg>
                        </div>
                        <p className="text-3xl font-bold text-gray-900 mb-2">{statsLoading ? '...' : statsError ? '0' : statsData.specialist}</p>
                        <p className="text-sm text-gray-600 font-medium">Total Specialists</p>
                    </div>
                </div>

                {/* Pending Patients Card */}
                <div className="bg-white rounded-2xl p-6 shadow-sm border border-yellow-500 hover:shadow-md transition-shadow">
                    <div className="flex flex-col items-center text-center">
                        <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center mb-4">
                            <svg className="w-6 h-6 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <p className="text-3xl font-bold text-gray-900 mb-2">{statsLoading ? '...' : statsError ? '0' : statsData.pendingPatients}</p>
                        <p className="text-sm text-gray-600 font-medium">Pending Patients</p>
                    </div>
                </div>
            </div>

            <div className="p-6 bg-white rounded shadow-md mx-auto space-y-12">
                {/* Area Chart - Full Width */}

                {/* Donut & Line Chart - Side by Side */}
                <div className="flex flex-col md:flex-row md:space-x-6 space-y-8 md:space-y-0">
                    {/* Donut Chart */}
                    <div className="w-full md:w-1/2 bg-white p-4 rounded shadow">
                        <h2 className="text-xl font-semibold mb-4 text-center text-gray-800">Doctors, Specialists & Patients</h2>
                        <div className="flex justify-center">
                            {loading ? (
                                <div className="text-center text-gray-500">Loading...</div>
                            ) : error ? (
                                <div className="text-center text-red-500">{error}</div>
                            ) : (
                                <Chart options={donutOptions} series={donutSeries} type="donut" height={320} />
                            )}
                        </div>
                    </div>

                    {/* Bar Chart: Doctors vs Patients */}
                    <div className="w-full md:w-1/2 bg-white p-4 rounded shadow">
                        <h2 className="text-xl font-semibold mb-4 text-center text-gray-800">Patients per Doctor</h2>
                        {doctorBarLoading ? (
                            <div className="text-center text-gray-500">Loading...</div>
                        ) : doctorBarError ? (
                            <div className="text-center text-red-500">{doctorBarError}</div>
                        ) : (
                            <Chart options={doctorBarOptions} series={doctorBarSeries} type="bar" height={320} />
                        )}
                    </div>
                </div>
                <div>
                    <h2 className="text-2xl font-bold text-center mb-4 text-gray-800">Monthly Appointments & Treatments</h2>
                    <Chart options={areaOptions} series={areaSeries} type="area" height={350} />
                </div>
            </div>
        </>
    );
};

export default DashboardCharts;
